"""
Data manager for unified access to different data sources.
"""
from datetime import date
from typing import Dict, List, Optional, Union

import pandas as pd

from app.core.data_collection.base import BaseDataCollector
from app.core.data_collection.cache import cache
from app.core.data_collection.yahoo_finance import YahooFinanceCollector
from app.core.data_collection.alpha_vantage import AlphaVantageCollector
from app.core.data_collection.financial_modeling_prep import FinancialModelingPrepCollector
from app.settings import get_settings
from app.utils.constants import CACHE_KEY_STOCK_DATA
from app.utils.logging import logger
from app.utils.validation import validate_tickers


class DataManager:
    """
    Data manager for unified access to different data sources.

    This class provides a unified interface for accessing data from different sources.
    It also handles caching of data to improve performance.
    """

    def __init__(self):
        """Initialize the data manager."""
        self.settings = get_settings()

        # Initialize data collectors
        self.collectors: Dict[str, BaseDataCollector] = {}

        # Add Yahoo Finance collector if enabled
        if self.settings.data_sources['yahoo_finance']['enabled']:
            self.collectors["yahoo_finance"] = YahooFinanceCollector()

        # Add Alpha Vantage collector if enabled
        if self.settings.data_sources['alpha_vantage']['enabled']:
            self.collectors["alpha_vantage"] = AlphaVantageCollector()

        # Add Financial Modeling Prep collector if enabled
        if self.settings.data_sources['financial_modeling_prep']['enabled']:
            self.collectors["financial_modeling_prep"] = FinancialModelingPrepCollector()

        # Set default collector
        self.default_collector = "yahoo_finance"

        logger.info(f"Data manager initialized with collectors: {list(self.collectors.keys())}")

    def _is_dummy_data(self, df: pd.DataFrame) -> bool:
        """Check if the DataFrame contains dummy/sample data."""
        if df.empty:
            return True

        # Check if all close prices are exactly the same (common in dummy data)
        if 'close' in df.columns:
            close_prices = df['close'].values
            if len(set(close_prices)) <= 2:  # Very little variation suggests dummy data
                return True

        # Check if the data has unrealistic patterns (all prices are round numbers)
        if 'close' in df.columns:
            close_prices = df['close'].values
            if all(price % 1 == 0 for price in close_prices):  # All whole numbers
                return True

        return False

    async def _try_alternative_sources(self, symbol: str, start_date: date, end_date: date,
                                     interval: str, exclude_source: Optional[str] = None) -> pd.DataFrame:
        """Try alternative data sources when the primary source fails."""
        # Define source priority order
        source_priority = ["yahoo_finance", "alpha_vantage", "financial_modeling_prep"]

        # Remove the excluded source from the list
        if exclude_source:
            source_priority = [s for s in source_priority if s != exclude_source]

        for source_name in source_priority:
            if source_name in self.collectors:
                try:
                    logger.info(f"Trying alternative source: {source_name} for {symbol}")
                    collector = self.collectors[source_name]
                    df = await collector.get_stock_data(symbol, start_date, end_date, interval)

                    # Check if we got real data
                    if not df.empty and not self._is_dummy_data(df):
                        logger.info(f"Successfully got data from {source_name} for {symbol}")
                        return df

                except Exception as e:
                    logger.warning(f"Alternative source {source_name} failed for {symbol}: {e}")
                    continue

        logger.warning(f"All alternative sources failed for {symbol}, returning empty DataFrame")
        return pd.DataFrame()

    async def get_stock_data(
        self,
        symbol: str,
        start_date: date,
        end_date: date,
        interval: str = "1d",
        source: Optional[str] = None,
        use_cache: bool = True
    ) -> pd.DataFrame:
        """
        Get historical stock data for a symbol.

        Args:
            symbol: The stock symbol
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            source: Data source to use (if None, uses default)
            use_cache: Whether to use cached data

        Returns:
            DataFrame with historical stock data
        """
        # Validate ticker
        symbol = validate_tickers(symbol)[0]

        # Check cache if enabled
        if use_cache:
            cache_key = CACHE_KEY_STOCK_DATA.format(
                symbol=symbol,
                interval=interval,
                start=start_date.isoformat(),
                end=end_date.isoformat()
            )

            cached_data = await cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Using cached data for {symbol}")
                return cached_data

        # Get collector
        collector = self._get_collector(source)

        # Get data with error handling and fallback logic
        try:
            df = await collector.get_stock_data(symbol, start_date, end_date, interval)

            # Check if we got dummy data (indicating rate limiting or failure)
            if self._is_dummy_data(df):
                logger.warning(f"Got dummy data for {symbol}, trying alternative sources")
                df = await self._try_alternative_sources(symbol, start_date, end_date, interval, source)

            # Cache data if enabled and it's not dummy data
            if use_cache and not df.empty and not self._is_dummy_data(df):
                await cache.set(cache_key, df)

            # Return the DataFrame
            return df

        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()

    async def get_multiple_stock_data(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        interval: str = "1d",
        source: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict[str, pd.DataFrame]:
        """
        Get historical stock data for multiple symbols.

        Args:
            symbols: List of stock symbols
            start_date: Start date for historical data
            end_date: End date for historical data
            interval: Data interval (e.g., '1d', '1wk', '1mo')
            source: Data source to use (if None, uses default)
            use_cache: Whether to use cached data

        Returns:
            Dictionary mapping symbols to DataFrames with historical stock data
        """
        # Initialize result dictionary
        result = {}

        try:
            # Validate tickers
            symbols = validate_tickers(symbols)

            # Track which symbols need to be fetched
            uncached_symbols = []

            # Check cache for each symbol if enabled
            if use_cache:
                for symbol in symbols:
                    cache_key = CACHE_KEY_STOCK_DATA.format(
                        symbol=symbol,
                        interval=interval,
                        start=start_date.isoformat(),
                        end=end_date.isoformat()
                    )

                    try:
                        cached_data = await cache.get(cache_key)
                        if cached_data is not None:
                            logger.debug(f"Using cached data for {symbol}")
                            result[symbol] = cached_data
                        else:
                            uncached_symbols.append(symbol)
                    except Exception as cache_error:
                        logger.warning(f"Error accessing cache for {symbol}: {cache_error}")
                        uncached_symbols.append(symbol)
            else:
                uncached_symbols = symbols

            # If all symbols were cached, return the result
            if not uncached_symbols:
                return result

            # Get collector
            collector = self._get_collector(source)

            # Get data for uncached symbols
            try:
                data = await collector.get_multiple_stock_data(
                    uncached_symbols, start_date, end_date, interval
                )

                # Cache data and update result
                for symbol, df in data.items():
                    if use_cache and not df.empty:
                        cache_key = CACHE_KEY_STOCK_DATA.format(
                            symbol=symbol,
                            interval=interval,
                            start=start_date.isoformat(),
                            end=end_date.isoformat()
                        )
                        try:
                            await cache.set(cache_key, df)
                        except Exception as cache_error:
                            logger.warning(f"Error setting cache for {symbol}: {cache_error}")

                    result[symbol] = df
            except Exception as fetch_error:
                logger.error(f"Error fetching data for symbols {uncached_symbols}: {fetch_error}")
                # Create empty DataFrames for symbols that couldn't be fetched
                for symbol in uncached_symbols:
                    if symbol not in result:
                        result[symbol] = pd.DataFrame()

        except Exception as e:
            logger.error(f"Error in get_multiple_stock_data: {e}")
            # Ensure we return a result for all requested symbols
            for symbol in symbols:
                if symbol not in result:
                    result[symbol] = pd.DataFrame()

        return result

    async def get_company_info(
        self,
        symbol: str,
        source: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict:
        """
        Get company information for a symbol.

        Args:
            symbol: The stock symbol
            source: Data source to use (if None, uses default)
            use_cache: Whether to use cached data

        Returns:
            Dictionary with company information
        """
        # Validate ticker
        symbol = validate_tickers(symbol)[0]

        # Check cache if enabled
        if use_cache:
            cache_key = f"company_info:{symbol}"

            cached_data = await cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Using cached company info for {symbol}")
                return cached_data

        # Get collector
        collector = self._get_collector(source)

        # Get data
        info = await collector.get_company_info(symbol)

        # Cache data if enabled
        if use_cache and info:
            await cache.set(cache_key, info)

        return info

    async def search_symbols(
        self,
        query: str,
        source: Optional[str] = None,
        use_cache: bool = True
    ) -> List[Dict]:
        """
        Search for symbols matching a query.

        Args:
            query: The search query
            source: Data source to use (if None, uses default)
            use_cache: Whether to use cached data

        Returns:
            List of dictionaries with symbol information
        """
        # Check cache if enabled
        if use_cache:
            cache_key = f"symbol_search:{query}"

            cached_data = await cache.get(cache_key)
            if cached_data is not None:
                logger.debug(f"Using cached symbol search for '{query}'")
                return cached_data

        # Get collector
        collector = self._get_collector(source)

        # Get data
        results = await collector.search_symbols(query)

        # Cache data if enabled
        if use_cache and results:
            await cache.set(cache_key, results)

        return results

    def _get_collector(self, source: Optional[str] = None) -> BaseDataCollector:
        """
        Get a data collector.

        Args:
            source: Data source to use (if None, uses default)

        Returns:
            Data collector instance

        Raises:
            ValueError: If the specified source is not available
        """
        if source is None:
            source = self.default_collector

        if source not in self.collectors:
            raise ValueError(f"Data source '{source}' is not available")

        return self.collectors[source]

    def combine_stock_data(self, data_dict):
        """
        Combine stock data from multiple symbols into a single DataFrame.

        Args:
            data_dict: Dictionary mapping symbols to DataFrames with historical stock data

        Returns:
            DataFrame with combined stock data, with 'close' prices for each symbol
        """
        if not data_dict:
            return pd.DataFrame()

        # Create a new DataFrame to store combined data
        combined_df = pd.DataFrame()

        # Process each symbol's data
        for symbol, df in data_dict.items():
            if df.empty:
                continue

            # Make sure the DataFrame has a datetime index
            if not isinstance(df.index, pd.DatetimeIndex):
                # If 'date' is a column, set it as index
                if 'date' in df.columns:
                    df = df.set_index('date')
                # Otherwise, try to convert the current index to datetime
                else:
                    try:
                        df.index = pd.to_datetime(df.index)
                    except Exception as idx_error:
                        logger.warning(f"Could not convert index to datetime for {symbol}: {idx_error}")
                        continue

            # Extract close prices and add to combined DataFrame
            if 'close' in df.columns:
                combined_df[symbol] = df['close']
            elif 'Close' in df.columns:  # Handle different column naming
                combined_df[symbol] = df['Close']
            else:
                logger.warning(f"No close price column found for {symbol}")

        # Sort by date
        combined_df = combined_df.sort_index()

        return combined_df


# Create a global data manager instance
data_manager = DataManager()
