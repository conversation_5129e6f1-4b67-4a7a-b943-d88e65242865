#!/usr/bin/env python3
"""
Test script to check data sources and fix rate limiting issues.
"""
import asyncio
import os
import sys
from datetime import date, timedelta

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.data_collection.data_manager import DataManager
from app.utils.logging import logger

async def test_data_sources():
    """Test all data sources to see which ones are working."""
    print("=" * 60)
    print("Testing Portfolio Optimization Data Sources")
    print("=" * 60)
    
    # Initialize data manager
    data_manager = DataManager()
    
    # Test symbols
    test_symbols = ["AAPL", "MSFT", "NVDA"]
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    print(f"Available collectors: {list(data_manager.collectors.keys())}")
    print(f"Testing symbols: {test_symbols}")
    print(f"Date range: {start_date} to {end_date}")
    print()
    
    # Test each collector individually
    for collector_name, collector in data_manager.collectors.items():
        print(f"Testing {collector_name}...")
        
        for symbol in test_symbols:
            try:
                df = await collector.get_stock_data(symbol, start_date, end_date, "1d")
                
                if df.empty:
                    print(f"  {symbol}: No data returned")
                elif len(df) < 5:
                    print(f"  {symbol}: Limited data ({len(df)} rows) - might be dummy data")
                else:
                    print(f"  {symbol}: ✓ Got {len(df)} rows of data")
                    
            except Exception as e:
                print(f"  {symbol}: ✗ Error - {str(e)}")
        
        print()
    
    # Test the data manager's fallback logic
    print("Testing Data Manager fallback logic...")
    for symbol in test_symbols:
        try:
            df = await data_manager.get_stock_data(symbol, start_date, end_date, "1d")
            
            if df.empty:
                print(f"  {symbol}: No data returned")
            elif len(df) < 5:
                print(f"  {symbol}: Limited data ({len(df)} rows) - might be dummy data")
            else:
                print(f"  {symbol}: ✓ Got {len(df)} rows of data")
                # Show a sample of the data
                print(f"    Sample: Close prices range from {df['close'].min():.2f} to {df['close'].max():.2f}")
                
        except Exception as e:
            print(f"  {symbol}: ✗ Error - {str(e)}")
    
    print("\n" + "=" * 60)
    print("Test completed!")

if __name__ == "__main__":
    asyncio.run(test_data_sources())
